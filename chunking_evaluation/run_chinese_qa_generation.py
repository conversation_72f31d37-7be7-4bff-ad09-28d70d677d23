"""
中文问答生成运行脚本
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from chunking_evaluation.evaluation_framework.synthetic_evaluation_chinese import SyntheticEvaluationChinese

# 全局路径配置 - 使用绝对路径确保路径正确
CURRENT_DIR = Path(__file__).parent.resolve()  # 获取脚本所在目录的绝对路径
DATASETS_DIR = CURRENT_DIR / "evaluation_framework" / "general_evaluation_data" / "datasets"
OUTPUT_DIR = CURRENT_DIR / "evaluation_framework" / "general_evaluation_data"
QUESTIONS_CSV_PATH = OUTPUT_DIR / "chinese_questions.csv"

# 确保输出目录存在
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

def get_all_dataset_paths():
    """获取datasets目录下所有.txt文件的路径"""
    if not DATASETS_DIR.exists():
        print(f"警告：datasets目录不存在: {DATASETS_DIR}")
        return []
    
    all_paths = []
    for subdir in DATASETS_DIR.iterdir():
        if subdir.is_dir():
            txt_files = list(subdir.glob('*.txt'))
            all_paths.extend([str(f) for f in txt_files])
    
    return all_paths

def print_dataset_info():
    """打印数据集信息"""
    paths = get_all_dataset_paths()
    print(f"找到 {len(paths)} 个文本文件:")
    
    for subdir in DATASETS_DIR.iterdir():
        if subdir.is_dir():
            subdir_name = subdir.name
            subdir_files = [p for p in paths if subdir_name in p]
            
            print(f"\n{subdir_name} ({len(subdir_files)} 个):")
            for path in subdir_files:
                print(f"  - {os.path.basename(path)}")

def print_statistics(df):
    """打印统计信息"""
    print(f"\n统计信息:")
    print(f"- 总问题数: {len(df)}")
    print(f"- 涉及语料库数: {df['corpus_id'].nunique()}")
    
    for subdir in DATASETS_DIR.iterdir():
        if subdir.is_dir():
            subdir_name = subdir.name
            subdir_count = len(df[df['corpus_id'].str.contains(subdir_name)])
            print(f"- {subdir_name}问题数: {subdir_count}")

def generate_chinese_qa():
    """为中文数据集生成问答对"""
    # 确保在正确的工作目录下运行
    original_cwd = os.getcwd()
    try:
        # 切换到脚本所在目录
        os.chdir(CURRENT_DIR)

        dataset_paths = get_all_dataset_paths()

        if not dataset_paths:
            print("未找到任何文本文件！")
            return

        print(f"找到 {len(dataset_paths)} 个文本文件")
        print(f"数据集目录: {DATASETS_DIR}")
        print(f"输出目录: {OUTPUT_DIR}")

        # 确保输出目录存在
        OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

        print("初始化中文问答生成器...")
        # 为ChromaDB指定明确的路径，避免在当前工作目录创建文件
        chroma_db_path = OUTPUT_DIR / "temp_chroma_db"
        synthetic_eval = SyntheticEvaluationChinese(
            corpora_paths=dataset_paths,
            queries_csv_path=str(QUESTIONS_CSV_PATH),
            chroma_db_path=str(chroma_db_path)
        )

        print("开始生成问答对...")
        print("参数设置: 精确模式, 每语料库5个问题, 1轮生成")

        synthetic_eval.generate_queries_and_excerpts(
            approximate_excerpts=False,  # 使用精确模式
            num_rounds=1,               # 生成1轮
            queries_per_corpus=5        # 每个语料库生成5个问题
        )

        print(f"问答对生成完成！输出文件: {QUESTIONS_CSV_PATH}")

        if QUESTIONS_CSV_PATH.exists():
            import pandas as pd
            df = pd.read_csv(QUESTIONS_CSV_PATH, encoding='utf-8')
            print_statistics(df)

    finally:
        # 恢复原始工作目录
        os.chdir(original_cwd)

def filter_chinese_qa():
    """过滤中文问答对的质量"""
    if not QUESTIONS_CSV_PATH.exists():
        print("问答文件不存在，请先生成问答对！")
        return
    
    dataset_paths = get_all_dataset_paths()
    
    synthetic_eval = SyntheticEvaluationChinese(
        corpora_paths=dataset_paths,
        queries_csv_path=str(QUESTIONS_CSV_PATH)
    )
    
    print("开始过滤低质量问答对...")
    synthetic_eval.filter_poor_excerpts(threshold=0.36)
    
    print("开始过滤重复问题...")
    synthetic_eval.filter_duplicates(threshold=0.78)
    
    print("过滤完成！")
    
    import pandas as pd
    df = pd.read_csv(QUESTIONS_CSV_PATH, encoding='utf-8')
    print_statistics(df)

def main():
    """主函数：生成和过滤中文问答对"""
    print("=== 中文数据集问答生成工具 ===")
    
    print("第一步：生成问答对")
    print("-" * 30)
    generate_chinese_qa()
    print()
    
    print("第二步：过滤问答对")
    print("-" * 30)
    filter_chinese_qa()
    print()
    
    print("=== 所有操作完成！ ===")
    print(f"生成的文件位置：{QUESTIONS_CSV_PATH}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="数据集问答生成工具")
    parser.add_argument("--action", choices=["generate", "filter", "both", "info"], 
                       default="both", help="执行的操作")
    
    args = parser.parse_args()
    
    if args.action == "info":
        print("=== 数据集信息 ===")
        print_dataset_info()
    elif args.action in ["generate", "both"]:
        print("=== 开始生成问答对 ===")
        generate_chinese_qa()
    
    if args.action in ["filter", "both"]:
        print("=== 开始过滤问答对 ===")
        filter_chinese_qa()
    
    if args.action == "both":
        print("所有操作完成！") 