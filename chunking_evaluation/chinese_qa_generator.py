"""
中文问答生成器

这个模块提供了专门用于生成中文问答对的功能，不依赖评估框架。
它可以自动从用户提供的中文语料库中生成问题-答案对。
"""

from typing import List
import os
import json
import random
import pandas as pd
from openai import OpenAI
from importlib import resources
from chunking_evaluation.utils import rigorous_document_search


class ChineseQAGenerator:
    """
    中文问答生成器
    
    专门用于从中文语料库生成问题-答案对，不包含评估功能。
    使用OpenAI的GPT模型自动生成高质量的问题和对应的参考答案。
    
    主要功能：
    - 自动从中文语料库生成问题和参考答案
    - 支持精确和近似两种参考答案提取模式
    - 提供质量过滤功能，移除低质量的问题-答案对
    - 支持去重功能，避免生成重复的问题
    """
    
    def __init__(self, corpora_paths: List[str], queries_csv_path: str):
        """
        初始化中文问答生成器
        
        参数:
            corpora_paths (List[str]): 语料库文件路径列表
            queries_csv_path (str): 保存生成问题的CSV文件路径
        """
        # 存储语料库路径列表
        self.corpora_paths = corpora_paths
        # 存储问题CSV文件路径
        self.questions_csv_path = queries_csv_path
        
        # 初始化OpenAI客户端
        self.client = OpenAI(
            api_key = "sk-ab756c213e1248aea064b2e49ad24de8",
            base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        )

        # 初始化合成问题数据框
        self.synth_questions_df = None
        
        # 加载提示词模板
        self._load_prompts()

    def _load_prompts(self):
        """加载问题生成的提示词模板"""
        try:
            # 获取提示词文件路径
            with resources.as_file(resources.files('chunking_evaluation.evaluation_framework') / 'prompts') as prompt_path:
                # 加载精确模式的系统提示（中文）
                with open(os.path.join(prompt_path, 'question_maker_system_chinese.txt'), 'r', encoding='utf-8') as f:
                    self.question_maker_system_prompt = f.read()
                
                # 加载精确模式的用户提示（中文）
                with open(os.path.join(prompt_path, 'question_maker_user_chinese.txt'), 'r', encoding='utf-8') as f:
                    self.question_maker_user_prompt = f.read()
                
                # 加载近似模式的系统提示（中文）
                with open(os.path.join(prompt_path, 'question_maker_approx_system_chinese.txt'), 'r', encoding='utf-8') as f:
                    self.question_maker_approx_system_prompt = f.read()
                
                # 加载近似模式的用户提示（中文）
                with open(os.path.join(prompt_path, 'question_maker_approx_user_chinese.txt'), 'r', encoding='utf-8') as f:
                    self.question_maker_approx_user_prompt = f.read()
        except Exception as e:
            print(f"警告：无法加载提示词文件: {e}")
            # 设置默认提示词
            self.question_maker_system_prompt = "你是一个专业的中文问答生成助手。"
            self.question_maker_user_prompt = "请根据以下文档生成问题和答案：{document}"
            self.question_maker_approx_system_prompt = "你是一个专业的中文问答生成助手。"
            self.question_maker_approx_user_prompt = "请根据以下文档生成问题和答案：{document}"

    def _save_questions_df(self):
        """
        保存合成问题数据框到CSV文件
        
        这个私有方法将当前的合成问题数据框保存到指定的CSV文件中。
        """
        # 确保使用UTF-8编码并添加BOM以避免乱码
        self.synth_questions_df.to_csv(
            self.questions_csv_path, 
            index=False, 
            encoding='utf-8-sig',  # 使用utf-8-sig添加BOM
            escapechar='\\',       # 转义字符
            quoting=1              # 引用所有字段
        )

    def _get_synth_questions_df(self):
        """
        获取或创建合成问题数据框
        
        这个私有方法尝试从CSV文件加载现有的问题数据，
        如果文件不存在则创建一个空的数据框。
        
        返回:
            pandas.DataFrame: 包含问题数据的数据框
        """
        if os.path.exists(self.questions_csv_path):
            # 如果CSV文件存在，加载现有数据
            synth_questions_df = pd.read_csv(self.questions_csv_path, encoding='utf-8')
        else:
            # 如果文件不存在，创建空的数据框
            synth_questions_df = pd.DataFrame(columns=['question', 'references', 'corpus_id'])
        return synth_questions_df

    def _tag_text(self, text, chunk_size=500):
        """
        为文本添加分块标签
        
        这个方法将长文本分割成较小的块，并为每个块添加标签。
        用于近似模式的问题生成，AI可以通过标签指定参考答案的位置。
        
        参数:
            text (str): 要标记的文本
            chunk_size (int): 每个分块的大小，默认500字符
            
        返回:
            tuple: (标记后的文本, 标签索引列表)
        """
        chunks = []
        tag_indexes = []
        
        # 将文本分割成指定大小的块
        for i in range(0, len(text), chunk_size):
            chunk = text[i:i+chunk_size]
            tag = f"[CHUNK_{len(chunks)}]"
            chunks.append(f"{tag}{chunk}")
            tag_indexes.append(i)  # 记录每个标签在原文中的位置
        
        tagged_text = "\n".join(chunks)
        return tagged_text, tag_indexes

    def _extract_question_and_references(self, corpus, document_length=4000, prev_questions=[], start_offset=None):
        """
        使用精确模式从语料库中提取问题和参考答案
        
        这个方法直接使用原始文本生成问题，AI模型需要返回精确的文本片段作为参考答案。
        然后使用严格的文档搜索来定位这些参考答案在原文中的确切位置。
        
        参数:
            corpus (str): 完整的语料库文本
            document_length (int): 用于生成问题的文档片段长度，默认4000字符
            prev_questions (list): 之前生成的问题列表，用于避免重复
            start_offset (int, 可选): 文档片段的起始偏移量，用于重试时选择不同片段
            
        返回:
            tuple: (question, references)
                - question: 生成的问题文本
                - references: 参考答案列表，每个元素为(内容, 起始位置, 结束位置)
        """
        # 如果语料库太长，选择一个片段
        if len(corpus) > document_length:
            if start_offset is None:
                # 随机选择一个起始位置
                start_index = random.randint(0, len(corpus) - document_length)
            else:
                # 使用指定的起始位置，但确保不超出边界
                start_index = min(start_offset, len(corpus) - document_length)
                start_index = max(0, start_index)
            document = corpus[start_index : start_index + document_length]
        else:
            start_index = 0
            document = corpus

        # 构建之前问题的字符串，用于避免重复
        if prev_questions:
            prev_questions_str = "之前已生成的问题：\n" + "\n".join([f"- {q}" for q in prev_questions[-5:]])  # 只显示最近5个问题
        else:
            prev_questions_str = "这是第一个问题。"

        # 调用OpenAI API生成问题和参考答案
        try:
            completion = self.client.chat.completions.create(
                model="qwen-plus-2025-07-14",
                response_format={ "type": "json_object" },  # 要求返回JSON格式
                max_tokens=600,
                messages=[
                    {"role": "system", "content": self.question_maker_system_prompt},
                    {"role": "user", "content": self.question_maker_user_prompt.replace("{document}", document).replace("{prev_questions_str}", prev_questions_str)}
                ]
            )
        except Exception as e:
            error_str = str(e)
            if "data_inspection_failed" in error_str or "inappropriate content" in error_str:
                # 如果是内容审核失败，抛出特定异常
                raise Exception(f"内容审核失败：{e}")
            else:
                # 其他API错误，直接抛出
                raise e
        
        # 解析AI返回的JSON响应
        json_response = json.loads(completion.choices[0].message.content)
        
        # 提取问题和参考答案
        try:
            text_references = json_response['references']
        except KeyError:
            raise ValueError("响应中不包含'references'字段。")
        try:
            question = json_response['question']
        except KeyError:
            raise ValueError("响应中不包含'question'字段。")

        # 处理参考答案，在原文中精确定位每个参考答案
        references = []
        for reference in text_references:
            # 验证参考答案是字符串类型
            if not isinstance(reference, str):
                raise ValueError(f"期望参考答案为字符串类型，但得到了{type(reference).__name__}")
            
            # 使用严格的文档搜索在语料库中查找参考答案的确切位置
            target = rigorous_document_search(corpus, reference)
            if target is not None:
                reference, start_index, end_index = target
                references.append((reference, start_index, end_index))
            else:
                raise ValueError(f"在文档中未找到给定参考答案的匹配项。\n参考答案：{reference}")
        
        return question, references

    def generate_queries_and_excerpts(self, approximate_excerpts=False, num_rounds=1, queries_per_corpus=5):
        """
        生成问题和摘录的主要方法
        
        这个方法是问答生成的核心功能，它为所有语料库生成问题-答案对。
        可以控制生成的轮数和每个语料库的问题数量。
        
        参数:
            approximate_excerpts (bool): 是否使用近似模式生成摘录，默认False（精确模式）
            num_rounds (int): 生成轮数，默认1
            queries_per_corpus (int): 每个语料库每轮生成的问题数量，默认5个
        """
        # 获取或创建合成问题数据框
        self.synth_questions_df = self._get_synth_questions_df()

        rounds = 0  # 轮数计数器
        # 循环生成问题，直到达到指定轮数
        while rounds < num_rounds:
            # 为每个语料库生成问题
            for corpus_id in self.corpora_paths:
                self._generate_corpus_questions(corpus_id, approx=approximate_excerpts, n=queries_per_corpus)
            rounds += 1  # 完成一轮，计数器加1

    def _generate_corpus_questions(self, corpus_id, approx=False, n=5):
        """
        为指定语料库生成问题和参考答案

        这个私有方法为单个语料库生成指定数量的问题-答案对。
        它会重试失败的生成尝试，直到成功生成所需数量的问题。

        参数:
            corpus_id (str): 语料库文件路径
            approx (bool): 是否使用近似模式生成参考答案，默认False（使用精确模式）
            n (int): 要生成的问题数量，默认5个
        """
        # 读取语料库文件内容
        with open(corpus_id, 'r', encoding='utf-8') as file:
            corpus = file.read()

        # 检查当前语料库已生成的问题数量
        existing_questions = self.synth_questions_df[self.synth_questions_df['corpus_id'] == corpus_id]
        existing_count = len(existing_questions)

        if existing_count >= n:
            print(f"语料库 {corpus_id} 已生成 {existing_count} 个问题，已达到目标数量 {n}，跳过生成")
            return

        print(f"语料库 {corpus_id} 当前已有 {existing_count} 个问题，需要生成 {n - existing_count} 个新问题")

        remaining_count = n - existing_count
        max_retries_per_question = 5  # 每个问题的最大重试次数
        i = 0  # 成功生成的问题计数器

        # 循环直到生成足够数量的问题
        while i < remaining_count:
            # 内层循环用于重试失败的生成尝试
            retry_count = 0
            while retry_count < max_retries_per_question:
                try:
                    print(f"正在尝试生成第{existing_count + i + 1}个问题（第{retry_count + 1}次尝试）")

                    # 获取当前语料库已生成的问题列表，用于避免重复
                    questions_list = self.synth_questions_df[self.synth_questions_df['corpus_id'] == corpus_id]['question'].tolist()

                    # 根据模式选择不同的问题生成方法
                    if approx:
                        # 近似模式暂不实现，使用精确模式
                        question, references = self._extract_question_and_references(corpus, 4000, questions_list)
                    else:
                        # 在重试时使用不同的文档片段偏移量
                        start_offset = None
                        if retry_count > 0:
                            # 每次重试使用不同的偏移量，避免重复的内容审核问题
                            start_offset = (retry_count * 1000) % max(1, len(corpus) - 4000)
                        question, references = self._extract_question_and_references(corpus, 4000, questions_list, start_offset)

                    # 限制参考答案数量，避免过于复杂的问题
                    if len(references) > 3:
                        raise ValueError("参考答案数量超过3个。")

                    # 将参考答案转换为字典格式
                    references = [{'content': ref[0], 'start_index': ref[1], 'end_index': ref[2]} for ref in references]

                    # 创建新问题记录
                    new_question = {
                        'question': question,
                        'references': json.dumps(references, ensure_ascii=False, indent=None),  # 确保中文不被转义
                        'corpus_id': corpus_id
                    }

                    # 将新问题添加到数据框中
                    new_df = pd.DataFrame([new_question])
                    self.synth_questions_df = pd.concat([self.synth_questions_df, new_df], ignore_index=True)

                    # 立即保存到文件，防止数据丢失
                    self._save_questions_df()

                    print(f"成功生成第{existing_count + i + 1}个问题")
                    break  # 成功生成，跳出重试循环

                except ValueError as e:
                    print(f"生成过程中发生错误：{e}")
                    retry_count += 1
                    continue  # 重试生成

                except Exception as e:
                    print(f"生成过程中发生未预期错误：{e}")
                    retry_count += 1
                    continue  # 重试生成

            # 如果重试次数用完仍未成功，跳过这个问题
            if retry_count >= max_retries_per_question:
                print(f"第{existing_count + i + 1}个问题生成失败，已达到最大重试次数，跳过")
            else:
                i += 1  # 成功生成一个问题，计数器加1
